#!/bin/bash

AUTH_FILE="/tmp/.proxmox-auth"

# Load auth
if [[ ! -f "$AUTH_FILE" ]]; then
    echo "[!] File autentikasi tidak ditemukan."
    exit 1
fi
source "$AUTH_FILE"

if [[ -z "$PVE_TICKET" || -z "$PVE_CSRF" || -z "$PVE_HOST" ]]; then
    echo "[!] Variabel autentikasi tidak lengkap."
    exit 1
fi

# Parameter
NODE="pve-meta01"
VMID="$1"

if [[ -z "$VMID" ]]; then
    echo "Usage: $0 <vmid>"
    exit 1
fi

echo "[*] Men-suspend VM ID $VMID..."

RESPONSE=$(curl -sk -w "\n%{http_code}" -X POST "$PVE_HOST/api2/json/nodes/$NODE/qemu/$VMID/status/suspend" \
  -H "Cookie: PVEAuthCookie=$PVE_TICKET" \
  -H "CSRFPreventionToken: $PVE_CSRF")

CODE=$(echo "$RESPONSE" | tail -n1)
BODY=$(echo "$RESPONSE" | head -n -1)

if [[ "$CODE" == "200" ]]; then
    echo "[+] VM $VMID berhasil disuspend."
else
    echo "[!] Gagal suspend VM $VMID. HTTP $CODE"
    echo "$BODY"
fi
