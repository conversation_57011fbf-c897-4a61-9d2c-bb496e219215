#!/bin/bash

# --- Konfigurasi ---
NODE="pve-meta01"  # Nama node Proxmox
AUTH_FILE="/tmp/.proxmox-auth"

# --- Validasi dependensi ---
if ! command -v curl >/dev/null 2>&1 || ! command -v jq >/dev/null 2>&1; then
    echo "[!] Error: curl dan jq harus diinstall. Jalankan: apt install curl jq"
    exit 1
fi

# --- Source file autentikasi ---
if [[ ! -f "$AUTH_FILE" ]]; then
    echo "[!] Error: File autentikasi $AUTH_FILE tidak ditemukan. Jalankan auth.sh terlebih dahulu."
    exit 1
fi

source "$AUTH_FILE"

# Validasi variabel autentikasi
if [[ -z "$PVE_TICKET" || -z "$PVE_CSRF" || -z "$PVE_HOST" ]]; then
    echo "[!] Error: Token autentikasi atau host tidak lengkap. Periksa $AUTH_FILE."
    exit 1
fi

# --- Ambil daftar VM ---
echo "[*] Mengambil daftar VM dari node '$NODE'..."
VM_RESPONSE=$(curl -s -k -H "CSRFPreventionToken: $PVE_CSRF" \
    -b "PVEAuthCookie=$PVE_TICKET" \
    "$PVE_HOST/api2/json/nodes/$NODE/qemu")

# --- Validasi respon ---
if [[ -z "$VM_RESPONSE" ]]; then
    echo "[!] Error: Gagal mengambil daftar VM dari node $NODE."
    exit 1
fi

# --- Filter template == 1 dan tampilkan ---
echo "[+] Daftar Template VM:"
echo "--------------------------"
TEMPLATES=$(echo "$VM_RESPONSE" | jq -r '.data[] | select(.template == 1) | "\(.vmid) | \(.name // "Unnamed")"')

if [[ -z "$TEMPLATES" ]]; then
    echo "[!] Tidak ada template KVM yang ditemukan di node $NODE."
else
    echo "$TEMPLATES"
    echo "[+] Selesai mengambil daftar template KVM."
fi