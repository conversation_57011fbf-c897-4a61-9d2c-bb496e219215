#!/bin/bash

AUTH_FILE="/tmp/.proxmox-auth"

# Load auth
if [[ ! -f "$AUTH_FILE" ]]; then
    echo "[!] File autentikasi tidak ditemukan."
    exit 1
fi
source "$AUTH_FILE"

if [[ -z "$PVE_TICKET" || -z "$PVE_CSRF" || -z "$PVE_HOST" ]]; then
    echo "[!] Variabel autentikasi tidak lengkap."
    exit 1
fi

NODE="pve-meta01"
VMID="$1"

if [[ -z "$VMID" ]]; then
    echo "Usage: $0 <vmid>"
    exit 1
fi

echo "[*] Menyalakan VM ID $VMID..."

RESPONSE=$(curl -sk -w "\n%{http_code}" -X POST "$PVE_HOST/api2/json/nodes/$NODE/qemu/$VMID/status/start" \
  -H "Cookie: PVEAuthCookie=$PVE_TICKET" \
  -H "CSRFPreventionToken: $PVE_CSRF")

CODE=$(echo "$RESPONSE" | tail -n1)
BODY=$(echo "$RESPONSE" | head -n -1)

if [[ "$CODE" == "200" ]]; then
    echo "[+] VM $VMID berhasil dinyalakan."
else
    echo "[!] Gagal menyalakan VM $VMID. HTTP $CODE"
    echo "$BODY"
fi
