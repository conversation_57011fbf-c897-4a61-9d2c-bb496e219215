#!/bin/bash

AUTH_FILE="/tmp/.proxmox-auth"

# --- Load Proxmox Auth ---
if [[ ! -f "$AUTH_FILE" ]]; then
    echo "[!] File autentikasi tidak ditemukan. Jalankan auth.sh terlebih dahulu."
    exit 1
fi

source "$AUTH_FILE"

if [[ -z "$PVE_TICKET" || -z "$PVE_CSRF" || -z "$PVE_HOST" ]]; then
    echo "[!] Variabel autentikasi tidak lengkap."
    exit 1
fi

# --- Parameter Konfigurasi ---
NODE="pve-meta01"
TEMPLATE_ID="9001"
VMID="9003"
VM_NAME="vm-kvm-example"
STORAGE="storage"
DISK_SIZE="20"  # dalam GB, tanpa satuan
CORES=2
RAM=2048
NET_BRIDGE="vmbr1"
CIUSER="root"
CIPASSWORD="password123"
SSHKEYS="ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIEvRfOqdzcGEuCqiRZr5wMxwvU4cl3GWWzpPZfjcIabL DokoMaze"
IP_ADDRESS="*********/24"
GATEWAY="********"

# encode SSH keys
SSHKEYS=$(echo "$SSHKEYS" | jq -sRr @uri)

echo "[*] Mengkloning template ID $TEMPLATE_ID ke VM ID $VMID..."

CLONE_RESP=$(curl -sk -w "\n%{http_code}" -X POST "$PVE_HOST/api2/json/nodes/$NODE/qemu/$TEMPLATE_ID/clone" \
    -H "Cookie: PVEAuthCookie=$PVE_TICKET" \
    -H "CSRFPreventionToken: $PVE_CSRF" \
    -d "newid=$VMID" \
    -d "name=$VM_NAME" \
    -d "target=$NODE" \
    -d "full=1")

CLONE_CODE=$(echo "$CLONE_RESP" | tail -n1)
if [[ "$CLONE_CODE" != "200" ]]; then
    echo "[!] Gagal clone. HTTP $CLONE_CODE"
    echo "$CLONE_RESP"
    exit 1
fi

echo "[*] Mengatur konfigurasi VM (CPU, RAM, Disk, Network, Cloud-Init)..."

CONFIG_RESP=$(curl -sk -w "\n%{http_code}" -X POST "$PVE_HOST/api2/json/nodes/$NODE/qemu/$VMID/config" \
    -H "Cookie: PVEAuthCookie=$PVE_TICKET" \
    -H "CSRFPreventionToken: $PVE_CSRF" \
    --data-urlencode "cores=$CORES" \
    --data-urlencode "memory=$RAM" \
    --data-urlencode "scsi0=$STORAGE:$DISK_SIZE" \
    --data-urlencode "net0=virtio,bridge=$NET_BRIDGE" \
    --data-urlencode "ciuser=$CIUSER" \
    --data-urlencode "cipassword=$CIPASSWORD" \
    --data-urlencode "sshkeys=$SSHKEYS" \
    --data-urlencode "ipconfig0=ip=$IP_ADDRESS,gw=$GATEWAY")

CONFIG_CODE=$(echo "$CONFIG_RESP" | tail -n1)
if [[ "$CONFIG_CODE" != "200" ]]; then
    echo "[!] Gagal set config. HTTP $CONFIG_CODE"
    echo "$CONFIG_RESP"
    exit 1
fi

echo "[*] Menyalakan VM..."

START_RESP=$(curl -sk -w "\n%{http_code}" -X POST "$PVE_HOST/api2/json/nodes/$NODE/qemu/$VMID/status/start" \
    -H "Cookie: PVEAuthCookie=$PVE_TICKET" \
    -H "CSRFPreventionToken: $PVE_CSRF")

START_CODE=$(echo "$START_RESP" | tail -n1)
if [[ "$START_CODE" != "200" ]]; then
    echo "[!] Gagal menyalakan VM. HTTP $START_CODE"
    echo "$START_RESP"
    exit 1
fi

echo "[+] VM $VM_NAME (ID $VMID) berhasil dikloning, dikonfigurasi, dan dijalankan."
