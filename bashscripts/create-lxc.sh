#!/bin/bash

# --- File penyimpanan token autentikasi ---
AUTH_FILE="/tmp/.proxmox-auth"

# --- Memuat autentikasi dari file ---
if [[ ! -f "$AUTH_FILE" ]]; then
    echo "[!] File autentikasi $AUTH_FILE tidak ditemukan. Jalankan script login terlebih dahulu."
    exit 1
fi

source "$AUTH_FILE"

# --- Validasi variabel autentikasi ---
if [[ -z "$PVE_TICKET" || -z "$PVE_CSRF" || -z "$PVE_HOST" ]]; then
    echo "[!] Variabel autentikasi (PVE_TICKET, PVE_CSRF, atau PVE_HOST) tidak lengkap."
    exit 1
fi

# --- Konfigurasi container ---
NODE="pve-meta01"                    # Nama node Proxmox
CONTAINER_ID=4125             # ID container
CONTAINER_NAME="my-container"
TEMPLATE="local:vztmpl/debian-12-standard_12.7-1_amd64.tar.zst"
STORAGE="storage"          # Nama storage Proxmox
MEMORY=1024
CPU_CORES=2
DISK_SIZE=8                   # Ukuran disk dalam GB, tanpa 'G'
HOSTNAME="mycontainer"
IP_ADDRESS="*********/24"
GATEWAY="********"
DNS="*******"
BRIDGE="vmbr1"
ROOT_PASSWORD="your_secure_password"

# --- Validasi dependensi ---
if ! command -v curl >/dev/null 2>&1 || ! command -v jq >/dev/null 2>&1; then
    echo "[!] curl dan jq harus diinstall! Jalankan: apt install curl jq"
    exit 1
fi

# --- Format parameter net0 (menggunakan format yang sudah benar) ---
NET0="name=eth0,bridge=$BRIDGE,ip=$IP_ADDRESS,gw=$GATEWAY"

# --- Debugging: Tampilkan nilai net0 untuk pemeriksaan ---
echo "Parameter net0: $NET0"

ENCODED_NET0=$(printf %s "$NET0" | jq -sRr @uri)
echo "Encoded net0: $ENCODED_NET0"

# --- Membuat container LXC melalui API ---
echo "Membuat container LXC..."
CREATE_RESPONSE=$(curl -s -k -X POST \
    -H "CSRFPreventionToken: $PVE_CSRF" \
    -b "PVEAuthCookie=$PVE_TICKET" \
    "$PVE_HOST/api2/json/nodes/$NODE/lxc" \
    -d "vmid=$CONTAINER_ID" \
    -d "ostemplate=$TEMPLATE" \
    -d "storage=$STORAGE" \
    -d "memory=$MEMORY" \
    -d "cores=$CPU_CORES" \
    -d "rootfs=$STORAGE:$DISK_SIZE" \
    -d "hostname=$HOSTNAME" \
    -d "password=$ROOT_PASSWORD" \
    -d "net0=$ENCODED_NET0" \
    -d "nameserver=$DNS")

# --- Memeriksa hasil pembuatan container ---
UPID=$(echo "$CREATE_RESPONSE" | jq -r '.data // empty')
if [ -z "$UPID" ]; then
    echo "[!] Gagal membuat container! Respon: $CREATE_RESPONSE"
    exit 1
fi

echo "Container sedang dibuat dengan UPID: $UPID"

# --- Menunggu proses pembuatan selesai ---
echo "Menunggu pembuatan container selesai..."
while true; do
    STATUS=$(curl -s -k -H "CSRFPreventionToken: $PVE_CSRF" \
        -b "PVEAuthCookie=$PVE_TICKET" \
        "$PVE_HOST/api2/json/nodes/$NODE/tasks/$UPID/status" | jq -r '.data.status // empty')
    
    if [ "$STATUS" == "stopped" ]; then
        EXIT_STATUS=$(curl -s -k -H "CSRFPreventionToken: $PVE_CSRF" \
            -b "PVEAuthCookie=$PVE_TICKET" \
            "$PVE_HOST/api2/json/nodes/$NODE/tasks/$UPID/status" | jq -r '.data.exitstatus // empty')
        if [ "$EXIT_STATUS" == "OK" ]; then
            echo "[+] Container berhasil dibuat!"
            break
        else
            echo "[!] Gagal membuat container: $EXIT_STATUS"
            exit 1
        fi
    fi
    sleep 2
done

# --- Memulai container ---
echo "Memulai container..."
curl -s -k -X POST \
    -H "CSRFPreventionToken: $PVE_CSRF" \
    -b "PVEAuthCookie=$PVE_TICKET" \
    "$PVE_HOST/api2/json/nodes/$NODE/lxc/$CONTAINER_ID/status/start"

# --- Memeriksa status container ---
echo "Memeriksa status container..."
sleep 5
STATUS=$(curl -s -k -H "CSRFPreventionToken: $PVE_CSRF" \
    -b "PVEAuthCookie=$PVE_TICKET" \
    "$PVE_HOST/api2/json/nodes/$NODE/lxc/$CONTAINER_ID/status/current" | jq -r '.data.status // empty')

echo "[+] Container $CONTAINER_NAME telah dibuat dengan IP statik $IP_ADDRESS"
echo "Status container: $STATUS"