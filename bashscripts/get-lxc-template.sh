#!/bin/bash


# Source file auth
source /tmp/.proxmox-auth

# --- Konfigurasi ---
NODE="pve-meta01"
STORAGE="local"  # Ganti sesuai storage kamu

echo "[*] Fetching LXC image list..."

# Ambil response image
IMAGE_RESPONSE=$(curl -sk "$PVE_HOST/api2/json/nodes/$NODE/storage/$STORAGE/content" \
  -H "Cookie: PVEAuthCookie=$PVE_TICKET")

# Validasi JSON
echo "$IMAGE_RESPONSE" | jq -e . >/dev/null 2>&1
if [[ $? -ne 0 ]]; then
  echo "[!] ERROR: Response dari Proxmox bukan JSON valid:"
  echo "$IMAGE_RESPONSE"
  exit 1
fi

echo "$IMAGE_RESPONSE" | jq -r '
  .data[]
  | select(.content == "vztmpl")
  | .volid
  | sub("^.*vztmpl/"; "")
' | awk -F"-" '{ print $1 "-" $2 }' | sort -u


echo "$IMAGE_RESPONSE" | jq -r '.data[] | select(.content == "vztmpl") | .volid'
