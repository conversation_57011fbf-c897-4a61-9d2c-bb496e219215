#!/bin/bash

# --- Konfigurasi utama ---
URL="https://pve-meta01.cloudcentre.id:8006"
USER="root@pam"
PASS='uLtimate^85!!!'

# --- File penyimpanan token sementara ---
AUTH_FILE="/tmp/.proxmox-auth"

# --- Login ---
LOGIN_RESPONSE=$(curl -sk -X POST "$URL/api2/json/access/ticket" \
  -d "username=$USER" -d "password=$PASS")

TICKET=$(echo "$LOGIN_RESPONSE" | jq -r '.data.ticket')
CSRF=$(echo "$LOGIN_RESPONSE" | jq -r '.data.CSRFPreventionToken')

if [[ "$TICKET" == "null" || -z "$TICKET" ]]; then
  echo "[!] Login gagal."
  exit 1
fi

# Simpan ke file untuk di-source oleh script lain
cat <<EOF > "$AUTH_FILE"
# Proxmox Auth
export PVE_TICKET="$TICKET"
export PVE_CSRF="$CSRF"
export PVE_HOST="$URL"
EOF

echo "[+] Login berhasil. Token disimpan di $AUTH_FILE"
