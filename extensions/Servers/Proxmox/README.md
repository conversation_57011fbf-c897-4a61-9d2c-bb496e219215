# Paymenter Proxmox Extension

Extension ini memungkinkan Paymenter untuk mengelola Virtual Machine (KVM/QEMU) dan Container (LXC) di Proxmox Virtual Environment secara otomatis.

## 🚀 Fitur Utama

- ✅ **Manajemen VM & Container Lengkap**
  - Pembuatan KVM/QEMU dan LXC Container
  - Start, Stop, Restart, Suspend, Resume
  - Terminate dengan cleanup otomatis
  - Upgrade resources (CPU, Memory)

- ✅ **Manajemen IP Otomatis**
  - Alokasi IP otomatis dari range yang dikonfigurasi
  - Support untuk public dan private network
  - Tracking IP yang sudah digunakan

- ✅ **Integrasi API Proxmox Lengkap**
  - Autentikasi dengan ticket system
  - Template management untuk KVM dan LXC
  - VNC console untuk KVM
  - Task monitoring dan error handling

- ✅ **Konfigurasi Fleksibel**
  - Multiple storage support
  - Network bridge configuration
  - DNS server configuration
  - Bandwidth limiting

## 📋 Persyaratan

- Paymenter v2.0+
- Proxmox VE 7.0+
- PHP 8.1+
- User Proxmox dengan permission yang memadai

## 🔧 Instalasi

### 1. Copy Extension ke Paymenter

```bash
# Jika Anda menggunakan repository ini
cp -r extensions/Servers/Proxmox /path/to/paymenter/app/Extensions/Servers/

# Atau jika Anda download manual
mkdir -p /path/to/paymenter/app/Extensions/Servers/Proxmox
cp Proxmox.php /path/to/paymenter/app/Extensions/Servers/Proxmox/
```

### 2. Set Permission

```bash
chown -R www-data:www-data /path/to/paymenter/app/Extensions/Servers/Proxmox
chmod -R 755 /path/to/paymenter/app/Extensions/Servers/Proxmox
```

### 3. Clear Cache Paymenter

```bash
cd /path/to/paymenter
php artisan cache:clear
php artisan config:clear
```

## ⚙️ Konfigurasi

### 1. Konfigurasi Server Extension

Masuk ke Admin Panel Paymenter → Extensions → Servers → Proxmox

**Konfigurasi Wajib:**
- **Proxmox Server IP**: IP atau hostname server Proxmox
- **Port**: Port API Proxmox (default: 8006)
- **Username**: Username Proxmox (default: root@pam)
- **Password**: Password user Proxmox
- **Default Node**: Nama node Proxmox
- **Storage**: Storage default (contoh: local-lvm)
- **Public Network Bridge**: Bridge untuk network public (contoh: vmbr0)
- **Public IP Range**: Range IP public (contoh: ************* - *************)
- **Public Subnet (CIDR)**: Subnet mask (contoh: 24)
- **Public Gateway**: Gateway IP public

**Konfigurasi Opsional:**
- **LXC Template Storage**: Storage khusus untuk template LXC
- **Private Network Bridge**: Bridge untuk network private
- **Private IP Range**: Range IP private
- **Private Subnet (CIDR)**: Subnet mask private
- **Private Gateway**: Gateway IP private
- **DNS Servers**: DNS servers (contoh: ******* *******)

### 2. Test Koneksi

Klik tombol "Test Connection" untuk memverifikasi konfigurasi.

### 3. Konfigurasi Produk

Buat produk baru dengan extension Proxmox:

**Product Settings:**
- **VM Type**: KVM/QEMU atau LXC Container
- **CPU Cores**: Jumlah CPU cores
- **Memory (MB)**: RAM dalam MB
- **Disk Size (GB)**: Ukuran disk dalam GB
- **Bandwidth Limit (MB/s)**: Limit bandwidth (0 = unlimited)
- **Network Type**: Public atau Private Network

## 🎯 Penggunaan

### Checkout Options

Saat customer order, mereka akan diminta:
- **Hostname**: Nama host untuk VM/Container
- **Template**: Template OS yang tersedia
- **Root Password**: Password root (opsional, auto-generate jika kosong)

### Actions Tersedia

Setelah VM/Container dibuat, customer dapat:
- Start/Stop/Restart VM
- VNC Console (khusus KVM)
- Lihat informasi IP dan credentials

## 🔍 Troubleshooting

### Error "Failed to authenticate with Proxmox"
- Periksa IP, port, username, dan password
- Pastikan user memiliki permission yang memadai
- Cek firewall Proxmox (port 8006)

### Error "No available IP addresses"
- Periksa konfigurasi IP range
- Pastikan format range benar: `IP_START - IP_END`
- Cek apakah masih ada IP available

### Error "Template not found"
- Pastikan template tersedia di storage yang dikonfigurasi
- Untuk LXC: template harus ada di storage dengan content type 'vztmpl'
- Untuk KVM: VM template harus ditandai sebagai template

### VM/Container tidak bisa start
- Cek log Proxmox di web interface
- Pastikan resource (CPU, RAM) tersedia di node
- Periksa konfigurasi network bridge

## 📚 Dokumentasi Lengkap

Lihat [PROXMOX_EXTENSION.md](../../../PROXMOX_EXTENSION.md) untuk dokumentasi teknis lengkap.

## 🤝 Support

Jika mengalami masalah:
1. Periksa log Paymenter di `storage/logs/`
2. Periksa log Proxmox di web interface
3. Pastikan semua konfigurasi sudah benar
4. Test koneksi API secara manual

## 📄 License

Extension ini mengikuti lisensi yang sama dengan Paymenter.
