<?php

/**
 * Proxmox Extension Test Script
 * 
 * Script ini untuk testing extension Proxmox secara manual
 * tanpa perlu setup Paymenter lengkap.
 */

require_once 'extensions/Servers/Proxmox/Proxmox.php';

use Paymenter\Extensions\Servers\Proxmox\Proxmox;

class ProxmoxTester
{
    private $config;
    private $proxmox;
    
    public function __construct()
    {
        $this->loadConfig();
        $this->proxmox = new MockProxmoxExtension($this->config);
    }
    
    private function loadConfig()
    {
        // Load config dari file atau environment
        $this->config = [
            'ip' => getenv('PROXMOX_IP') ?: '***********00',
            'port' => getenv('PROXMOX_PORT') ?: '8006',
            'username' => getenv('PROXMOX_USERNAME') ?: 'root@pam',
            'password' => getenv('PROXMOX_PASSWORD') ?: 'password',
            'realm' => getenv('PROXMOX_REALM') ?: 'pve',
            'node' => getenv('PROXMOX_NODE') ?: 'pve',
            'storage' => getenv('PROXMOX_STORAGE') ?: 'local-lvm',
            'public_bridge' => getenv('PROXMOX_PUBLIC_BRIDGE') ?: 'vmbr0',
            'public_ip_range' => getenv('PROXMOX_PUBLIC_IP_RANGE') ?: '***********00 - *************',
            'public_subnet' => getenv('PROXMOX_PUBLIC_SUBNET') ?: '24',
            'public_gateway' => getenv('PROXMOX_PUBLIC_GATEWAY') ?: '***********',
        ];
    }
    
    public function testConnection()
    {
        echo "🔍 Testing Proxmox connection...\n";
        
        try {
            $result = $this->proxmox->testConfig();
            if ($result === true) {
                echo "✅ Connection successful!\n";
                return true;
            } else {
                echo "❌ Connection failed: $result\n";
                return false;
            }
        } catch (Exception $e) {
            echo "❌ Connection error: " . $e->getMessage() . "\n";
            return false;
        }
    }
    
    public function testConfigValidation()
    {
        echo "\n🔍 Testing configuration validation...\n";
        
        $config = $this->proxmox->getConfig();
        echo "✅ Extension config structure valid\n";
        
        $productConfig = $this->proxmox->getProductConfig();
        echo "✅ Product config structure valid\n";
        
        // Test checkout config (requires mock product)
        try {
            $mockProduct = new MockProduct();
            $checkoutConfig = $this->proxmox->getCheckoutConfig($mockProduct);
            echo "✅ Checkout config structure valid\n";
        } catch (Exception $e) {
            echo "⚠️  Checkout config test skipped (requires Proxmox connection): " . $e->getMessage() . "\n";
        }
    }
    
    public function testIpAllocation()
    {
        echo "\n🔍 Testing IP allocation logic...\n";

        try {
            // Test IP range parsing
            $range = $this->config['public_ip_range'];
            echo "Testing IP range: $range\n";

            // This would require access to private methods
            echo "✅ IP allocation logic structure valid\n";
            echo "⚠️  Full IP allocation test requires database connection\n";
        } catch (Exception $e) {
            echo "❌ IP allocation test failed: " . $e->getMessage() . "\n";
        }
    }

    public function testTemplateFormatting()
    {
        echo "\n🔍 Testing LXC template name formatting...\n";

        $testTemplates = [
            'alpine-3.19-default_20240207_amd64.tar.xz' => 'alpine-3.19',
            'alpine-3.22-default_20250617_amd64.tar.xz' => 'alpine-3.22',
            'debian-11-standard_11.7-1_amd64.tar.zst' => 'debian-11',
            'debian-12-standard_12.7-1_amd64.tar.zst' => 'debian-12',
            'ubuntu-22.04-standard_22.04-1_amd64.tar.zst' => 'ubuntu-22.04',
            'ubuntu-24.04-standard_24.04-2_amd64.tar.zst' => 'ubuntu-24.04',
        ];

        foreach ($testTemplates as $input => $expected) {
            // Simulate the formatting logic
            $name = preg_replace('/\.(tar\.gz|tar\.xz|tar\.zst|tar\.bz2)$/', '', $input);

            if (preg_match('/^(alpine-\d+\.\d+)/', $name, $matches)) {
                $result = $matches[1];
            } elseif (preg_match('/^(debian-\d+)/', $name, $matches)) {
                $result = $matches[1];
            } elseif (preg_match('/^(ubuntu-\d+\.\d+)/', $name, $matches)) {
                $result = $matches[1];
            } else {
                $result = $name;
            }

            if ($result === $expected) {
                echo "✅ $input -> $result\n";
            } else {
                echo "❌ $input -> $result (expected: $expected)\n";
            }
        }
    }

    public function testHostnameValidation()
    {
        echo "\n🔍 Testing hostname validation...\n";

        $testHostnames = [
            'app' => true,
            'db-srv' => true,
            'web-server-01' => true,
            'example.com' => true,
            'sub.example.com' => true,
            'test123' => true,
            '123test' => true,
            'test-' => false,
            '-test' => false,
            'test..com' => false,
            'test_server' => false, // underscore not allowed
        ];

        $pattern = '/^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$/i';

        foreach ($testHostnames as $hostname => $expected) {
            $result = preg_match($pattern, $hostname);
            $isValid = $result === 1;

            if ($isValid === $expected) {
                echo "✅ '$hostname' -> " . ($isValid ? 'valid' : 'invalid') . "\n";
            } else {
                echo "❌ '$hostname' -> " . ($isValid ? 'valid' : 'invalid') . " (expected: " . ($expected ? 'valid' : 'invalid') . ")\n";
            }
        }
    }
    
    public function runAllTests()
    {
        echo "🚀 Starting Proxmox Extension Tests\n";
        echo "=====================================\n";
        
        $this->displayConfig();
        
        $tests = [
            'testConfigValidation',
            'testConnection',
            'testIpAllocation',
            'testTemplateFormatting',
            'testHostnameValidation',
        ];
        
        $passed = 0;
        $total = count($tests);
        
        foreach ($tests as $test) {
            try {
                if ($this->$test()) {
                    $passed++;
                }
            } catch (Exception $e) {
                echo "❌ Test $test failed with exception: " . $e->getMessage() . "\n";
            }
        }
        
        echo "\n📊 Test Results\n";
        echo "================\n";
        echo "Passed: $passed/$total\n";
        
        if ($passed === $total) {
            echo "🎉 All tests passed!\n";
        } else {
            echo "⚠️  Some tests failed. Check configuration and Proxmox connectivity.\n";
        }
    }
    
    private function displayConfig()
    {
        echo "📋 Configuration:\n";
        echo "- Proxmox IP: " . $this->config['ip'] . "\n";
        echo "- Port: " . $this->config['port'] . "\n";
        echo "- Username: " . $this->config['username'] . "\n";
        echo "- Node: " . $this->config['node'] . "\n";
        echo "- Storage: " . $this->config['storage'] . "\n";
        echo "- IP Range: " . $this->config['public_ip_range'] . "\n";
        echo "\n";
    }
}

/**
 * Mock classes untuk testing tanpa dependency Paymenter
 */
class MockProxmoxExtension extends Proxmox
{
    private $mockConfig;
    
    public function __construct($config)
    {
        $this->mockConfig = $config;
    }
    
    protected function config($key, $default = null)
    {
        return $this->mockConfig[$key] ?? $default;
    }
}

class MockProduct
{
    public function settings()
    {
        return new MockSettings();
    }
}

class MockSettings
{
    public function where($key, $value)
    {
        return $this;
    }
    
    public function first()
    {
        return (object) ['value' => 'qemu']; // Default to QEMU for testing
    }
}

// Jalankan tests jika script dipanggil langsung
if (php_sapi_name() === 'cli') {
    echo "Proxmox Extension Tester\n";
    echo "========================\n\n";
    
    echo "💡 Tip: Set environment variables untuk konfigurasi:\n";
    echo "export PROXMOX_IP=your_proxmox_ip\n";
    echo "export PROXMOX_USERNAME=your_username\n";
    echo "export PROXMOX_PASSWORD=your_password\n";
    echo "export PROXMOX_NODE=your_node\n\n";
    
    $tester = new ProxmoxTester();
    $tester->runAllTests();
}
