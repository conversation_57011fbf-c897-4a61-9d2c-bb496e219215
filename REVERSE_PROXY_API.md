# 🛡️ Reverse Proxy & Port Forwarding API

Sistem ini adalah layanan berbasis **FastAPI** yang memung<PERSON>kan kamu:
- Mengelola domain reverse proxy ke backend internal
- Otomatis konfigurasi dan SSL via **Certbot**
- Mengatur **port forwarding** via `iptables`
- Mendukung **token-based authentication**
- <PERSON><PERSON><PERSON> diatur, di<PERSON><PERSON><PERSON> (suspend), dan di<PERSON> (unsuspend) secara dinamis

---

## 📁 Struktur Project

```
reverseproxy-api/
├── main.py                  # FastAPI App
├── nginx_gen.py             # Template config Nginx
├── certbot_util.py          # Certbot runner
├── firewall.py              # iptables forward manager
├── templates/nginx.j2       # Template reverse proxy
├── .env                     # API token
├── domain_db.json           # (auto) Mapping domain
├── forward_db.json          # (auto) Mapping forward
├── setup.sh                 # Install system
├── requirements.txt         # Python deps
├── reverseproxy-api.service # Systemd
└── postman_collection.json  # API testing
```

---

## 🔐 .env

```env
API_TOKEN=supersecrettoken123
```

---

## ⚙️ Setup Otomatis

```bash
chmod +x setup.sh
./setup.sh
```

---

## 🚀 Jalankan Manual

```bash
uvicorn main:app --host 0.0.0.0 --port 8000
```

---

## 🔗 Endpoint: Proxy Domain

### 📥 Tambah Proxy

```http
POST /proxy-domain
Authorization: Bearer {API_TOKEN}
Body:
{
  "domain": "example.com",
  "backend": "************:8080"
}
```

### 📤 List Proxy

```http
GET /proxy-domain
```

### 🔄 Ubah Backend

```http
PATCH /proxy-domain/example.com
Body: { "domain": "example.com", "backend": "************:8080" }
```

### ❌ Hapus Domain

```http
DELETE /proxy-domain/example.com
```

### 📴 Suspend Domain

```http
POST /proxy-domain/example.com/suspend
```

### ✅ Unsuspend Domain

```http
POST /proxy-domain/example.com/unsuspend
```

---

## 🔁 Endpoint: Port Forward

### 📥 Tambah Forwarding

```http
POST /port-forward
Body:
{
  "public_port": 2222,
  "target_ip": "*************",
  "target_port": 22
}
```

### 📤 List Forwarding

```http
GET /port-forward
```

### ❌ Hapus Forward

```http
DELETE /port-forward/2222
```

### 📴 Suspend Forward

```http
POST /port-forward/2222/suspend
```

### ✅ Unsuspend Forward

```http
POST /port-forward/2222/unsuspend
```

---

## 🔧 Tambahan

* Semua `suspend` hanya menonaktifkan sementara (tanpa hapus konfigurasi)
* Sistem menggunakan file `domain_db.json` dan `forward_db.json` sebagai store sederhana
* Untuk SSL, Certbot menggunakan HTTP challenge (`/.well-known/acme-challenge`)

---

## 🧪 Uji API

Import file `postman_collection.json` ke Postman atau Insomnia untuk pengujian cepat.

---