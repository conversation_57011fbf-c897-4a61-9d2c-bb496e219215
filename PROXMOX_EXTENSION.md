# Dokumentasi Ekstensi Paymenter Proxmox

Dokumen ini menyediakan panduan komprehensif mengenai Ekstensi Proxmox untuk Paymenter, mencakup konfigurasi, fungsionalitas, dan manajemen server virtual (VM) serta kontainer (CT) melalui integrasi API Proxmox.

## 1. Pendahuluan

Ekstensi Proxmox Paymenter memungkinkan Anda untuk mengotomatisasi penyediaan dan manajemen server virtual (KVM/QEMU) dan kontainer (LXC) di lingkungan Proxmox Virtual Environment (PVE) langsung dari panel Paymenter Anda. Ini mencakup fungsionalitas seperti pembuatan, penang<PERSON><PERSON>, penghentian, dan peningkatan sumber daya VM/CT.

## 2. Persyaratan Sistem

Untuk menggunakan ekstensi ini, pastikan Anda memenuhi persyaratan berikut:

*   **Proxmox Virtual Environment (PVE)**: Ekstensi ini dirancang untuk berinteraksi dengan Proxmox VE. Pastikan instalasi Proxmox Anda berjalan dan dapat diakses melalui API.
*   **Akses API Proxmox**: <PERSON><PERSON> me<PERSON> kredensial (username dan password) dengan izin yang memadai di Proxmox untuk melakukan operasi manajemen VM/CT. Pengguna `root@pam` biasanya memiliki izin yang diperlukan.
*   **Paymenter**: Instalasi Paymenter yang berfungsi.
*   **PHP Extensions**: Ekstensi PHP yang diperlukan oleh Paymenter dan pustaka HTTP (misalnya, `curl` jika digunakan oleh pustaka HTTP Paymenter).

## 3. Konfigurasi Ekstensi (Pengaturan Server)

Setelah menginstal ekstensi Proxmox di Paymenter, Anda perlu mengkonfigurasinya melalui panel admin. Berikut adalah opsi konfigurasi yang tersedia:

*   **Proxmox Server IP (`ip`)**: Alamat IP atau hostname server Proxmox Anda.
*   **Port (`port`)**: Port API Proxmox (default: `8006`).
*   **Username (`username`)**: Nama pengguna Proxmox untuk otentikasi API (default: `root@pam`).
*   **Password (`password`)**: Kata sandi untuk nama pengguna Proxmox yang ditentukan.
*   **Realm (`realm`)**: Realm otentikasi Proxmox (default: `pve`).
*   **Default Node (`node`)**: Nama node Proxmox default tempat VM/CT akan dibuat.
*   **Storage (`storage`)**: Nama penyimpanan default di Proxmox (misalnya, `local-lvm`) tempat disk VM/CT akan dialokasikan.
*   **LXC Template Storage (`lxc_storage`)**: (Opsional) Nama penyimpanan khusus untuk template LXC (vztmpl). Jika kosong, akan menggunakan `Storage` utama.
*   **Public Network Bridge (`public_bridge`)**: Nama bridge jaringan di Proxmox untuk koneksi publik (misalnya, `vmbr0`).
*   **Public IP Range (`public_ip_range`)**: Rentang alamat IP publik yang akan dialokasikan untuk VM/CT (contoh: `************* - *************`). **Penting untuk alokasi IP otomatis.**
*   **Public Subnet (CIDR) (`public_subnet`)**: Subnet CIDR untuk jaringan publik (contoh: `24`).
*   **Public Gateway (`public_gateway`)**: Alamat gateway untuk jaringan publik.
*   **Private Network Bridge (`private_bridge`)**: (Opsional) Nama bridge jaringan di Proxmox untuk koneksi privat (misalnya, `vmbr1`).
*   **Private IP Range (`private_ip_range`)**: (Opsional) Rentang alamat IP privat (contoh: `********** - **********`). **Penting untuk alokasi IP otomatis jika menggunakan jaringan privat.**
*   **Private Subnet (CIDR) (`private_subnet`)**: (Opsional) Subnet CIDR untuk jaringan privat (contoh: `24`).
*   **Private Gateway (`private_gateway`)**: (Opsional) Alamat gateway untuk jaringan privat.
*   **DNS Servers (`dns_servers`)**: (Opsional) Daftar server DNS yang dipisahkan spasi (contoh: `******* *******`).

### Menguji Konfigurasi

Anda dapat menguji koneksi ke server Proxmox dengan mengklik tombol "Test Connection" atau sejenisnya di panel konfigurasi ekstensi. Ekstensi akan mencoba melakukan permintaan API sederhana ke `/version` untuk memverifikasi kredensial dan konektivitas.

## 4. Konfigurasi Produk (Pengaturan Produk)

Saat membuat produk baru di Paymenter yang akan menggunakan ekstensi Proxmox, Anda akan mengkonfigurasi parameter spesifik untuk VM/CT yang akan disediakan:

*   **VM Type (`vm_type`)**: Pilih antara `KVM/QEMU` (untuk mesin virtual penuh) atau `LXC Container` (untuk kontainer Linux).
*   **CPU Cores (`cores`)**: Jumlah core CPU yang akan dialokasikan untuk VM/CT.
*   **Memory (MB) (`memory`)**: Jumlah memori RAM dalam Megabyte (MB).
*   **Disk Size (GB) (`disk_size`)**: Ukuran disk penyimpanan dalam Gigabyte (GB).
*   **Bandwidth Limit (MB/s) (`bandwidth`)**: (Opsional) Batas bandwidth jaringan dalam Megabyte per detik (MB/s). Setel ke `0` untuk tidak ada batas.
*   **Network Type (`network_type`)**: Pilih jenis jaringan yang akan digunakan oleh VM/CT: `Public Network` atau `Private Network`.

## 5. Opsi Checkout

Saat pelanggan memesan produk Proxmox, mereka akan diminta untuk mengisi beberapa detail tambahan:

*   **Hostname (`hostname`)**: Nama host yang diinginkan untuk VM/CT.
*   **Template (`template`)**: Template sistem operasi atau kontainer yang akan digunakan. Daftar template akan diambil secara dinamis dari server Proxmox Anda.
    *   Untuk KVM/QEMU, ini bisa berupa template disk (misalnya, `ubuntu-20.04-cloudinit`).
    *   Untuk LXC Container, ini adalah template kontainer (misalnya, `ubuntu-20.04-standard`). Pastikan template LXC tersedia di penyimpanan yang dikonfigurasi.
*   **Root Password (`password`)**: Kata sandi untuk pengguna `root` (untuk Linux) atau administrator (untuk Windows) di VM/CT yang baru dibuat. Untuk KVM, ini opsional jika template sudah memiliki default.

## 6. Fungsionalitas Manajemen Server

Ekstensi Proxmox menyediakan berbagai fungsi untuk mengelola VM/CT setelah provisioning:

### Pembuatan Server (`createServer`)

Ketika layanan baru dibuat, ekstensi akan:
1.  Mencari VMID (Virtual Machine ID) berikutnya yang tersedia, dimulai dari 5000.
2.  Membuat pengguna Proxmox baru untuk klien (jika belum ada) dengan ID pengguna berbasis email klien (misalnya, `user@pve`).
3.  Mengalokasikan alamat IP yang tersedia secara otomatis dari rentang yang dikonfigurasi (publik atau privat) menggunakan fungsi internal ekstensi, dan menyimpannya sebagai properti layanan.
4.  Berdasarkan `vm_type` dan `template` yang dipilih, ekstensi akan melakukan operasi pembuatan VM/CT melalui API Proxmox, mirip dengan langkah-langkah yang dilakukan oleh skrip bash [`create-kvm.sh`](bashscript/create-kvm.sh) dan [`create-lxc.sh`](bashscript/create-lxc.sh):
    *   **Untuk KVM/QEMU**:
        *   VM akan dikloning dari template yang ada. Proses ini melibatkan dua langkah API:
            1.  Permintaan kloning ke `/nodes/{node}/qemu/{template_vmid}/clone` dengan `newid`, `name`, `full=1`, dan `target`.
            2.  Setelah kloning selesai, konfigurasi VM diperbarui melalui permintaan `PUT` ke `/nodes/{node}/qemu/{vmid}/config` dengan parameter seperti `cores`, `memory`, `cipassword` (jika ada), `ciuser` (default `root`), `ipconfig0` (IP, subnet, gateway), dan `net0` (untuk bandwidth).
        *   **Perbandingan dengan [`bashscripts/create-kvm.sh`](bashscript/create-kvm.sh)**: Skrip bash ini juga melakukan kloning VM dari template, mengatur konfigurasi (CPU, RAM, Disk, Network, Cloud-Init), dan kemudian menyalakan VM, semuanya melalui panggilan `curl` ke API Proxmox. Ekstensi PHP mengimplementasikan fungsionalitas yang setara.
    *   **Untuk LXC Container**:
        *   Kontainer baru akan dibuat melalui permintaan `POST` ke `/nodes/{node}/lxc` dengan parameter seperti `vmid`, `hostname`, `cores`, `memory`, `rootfs` (penyimpanan dan ukuran disk), `net0` (bridge, IP, subnet, gateway, bandwidth jika ada), `ostemplate`, `unprivileged`, `password` (jika ada), dan `nameserver` (jika dikonfigurasi).
        *   **Perbandingan dengan [`bashscripts/create-lxc.sh`](bashscript/create-lxc.sh)**: Skrip bash ini juga membuat kontainer LXC dengan parameter serupa (ID, template, storage, memori, CPU, disk, hostname, IP, gateway, DNS, password) melalui panggilan `curl` ke API Proxmox. Ekstensi PHP mengimplementasikan fungsionalitas yang setara.
5.  Menunggu hingga tugas pembuatan/konfigurasi di Proxmox selesai dengan memantau status tugas (`UPID`) melalui API Proxmox.
6.  Menyimpan VMID, tipe VM, dan alamat IP yang dialokasikan sebagai properti layanan di Paymenter untuk referensi dan manajemen di masa mendatang.

### Manajemen Alamat IP Otomatis

Ekstensi Proxmox Paymenter memiliki kemampuan untuk secara otomatis mengalokasikan alamat IP yang tersedia dari rentang yang telah Anda konfigurasikan (baik publik maupun privat). Proses ini memastikan setiap VM/CT yang baru dibuat mendapatkan alamat IP yang unik dan tidak bertabrakan.

Langkah-langkah internal yang dilakukan ekstensi:
1.  **Parsing Rentang IP**: Ekstensi akan mem-parsing string rentang IP yang dikonfigurasi (misalnya, "************* - *************") menjadi alamat IP awal dan akhir dalam format numerik (long integer). Ini dilakukan oleh metode `parseIpRange()`.
2.  **Mengambil IP yang Sudah Digunakan**: Ekstensi akan mengkueri database Paymenter untuk mendapatkan semua alamat IP yang saat ini dialokasikan untuk layanan lain yang dikelola oleh ekstensi Proxmox ini. Ini dilakukan oleh metode `getUsedIps()`.
3.  **Mencari IP yang Tersedia**: Dimulai dari alamat IP awal dalam rentang yang dikonfigurasi, ekstensi akan secara iteratif memeriksa setiap alamat IP. Jika alamat IP saat ini tidak ditemukan dalam daftar IP yang sudah digunakan, maka alamat IP tersebut dianggap tersedia dan akan dialokasikan untuk VM/CT yang baru. Proses ini ditangani oleh metode `findAvailableIp()`.
4.  **Penyimpanan Properti**: Setelah IP ditemukan dan dialokasikan, alamat IP tersebut beserta subnet dan gateway yang relevan akan disimpan sebagai properti layanan di Paymenter.

Jika tidak ada alamat IP yang tersedia dalam rentang yang dikonfigurasi, proses pembuatan server akan gagal dengan pesan kesalahan yang sesuai. Oleh karena itu, penting untuk memastikan bahwa rentang IP yang Anda sediakan memiliki cukup alamat untuk kebutuhan Anda.

### Suspend Server (`suspendServer`)

Menangguhkan VM atau kontainer yang sedang berjalan. Ini akan menyimpan status VM/CT ke disk dan membebaskan sumber daya CPU/memori.

### Unsuspend Server (`unsuspendServer`)

Melanjutkan VM atau kontainer yang sebelumnya ditangguhkan.

### Terminate Server (`terminateServer`)

Menghentikan dan menghapus VM atau kontainer secara permanen dari Proxmox. Fungsi ini juga akan melepaskan alamat IP yang dialokasikan dan menghapus properti terkait VM dari layanan Paymenter.

### Start VM (`startVm`)

Memulai VM atau kontainer yang sedang dalam status berhenti.

### Stop VM (`stopVm`)

Menghentikan VM atau kontainer yang sedang berjalan secara paksa (mirip dengan mematikan daya).

### Restart VM (`restartVm`)

Melakukan reboot VM atau kontainer.

### VNC Console (`getVncUrl`)

(Hanya untuk VM KVM/QEMU) Mengembalikan URL untuk mengakses konsol VNC berbasis web, memungkinkan pengguna berinteraksi langsung dengan VM.

### Upgrade Server (`upgradeServer`)

Memungkinkan perubahan pada sumber daya VM/CT seperti jumlah core CPU dan memori.

## 7. Catatan tentang Bash Scripts

Ekstensi Proxmox Paymenter (`extensions/Servers/Proxmox/Proxmox.php`) berinteraksi langsung dengan Proxmox API menggunakan permintaan HTTP. Ini adalah metode yang direkomendasikan dan paling terintegrasi.

Direktori `bashscript/` dalam proyek ini berisi contoh skrip bash seperti [`auth.sh.example`](bashscript/auth.sh.example), [`create-kvm.sh`](bashscript/create-kvm.sh), [`create-lxc.sh`](bashscript/create-lxc.sh), dan lainnya. Skrip-skrip ini menunjukkan bagaimana operasi Proxmox dapat dilakukan melalui command-line interface (CLI) menggunakan `curl` untuk berinteraksi dengan API Proxmox atau perintah `qm`/`pct` jika diinstal di server Proxmox itu sendiri.

Secara khusus, skrip [`create-kvm.sh`](bashscript/create-kvm.sh) mendemonstrasikan langkah-langkah manual untuk membuat VM KVM dengan kloning template, dan [`create-lxc.sh`](bashscript/create-lxc.sh) untuk membuat kontainer LXC, keduanya menggunakan panggilan API langsung melalui `curl`. Fungsionalitas yang setara diimplementasikan dalam metode `createServer` di ekstensi PHP, yang melakukan panggilan API yang setara secara terprogram.

Skrip-skrip ini **tidak secara langsung digunakan** oleh implementasi PHP ekstensi Proxmox ini. Jika ada kebutuhan untuk mengintegrasikan fungsionalitas dari skrip bash ini ke dalam ekstensi Paymenter, Anda perlu memodifikasi kode PHP di `Proxmox.php` untuk memanggil skrip eksternal ini menggunakan fungsi eksekusi shell PHP (misalnya, `shell_exec()` atau `exec()`). Namun, pendekatan ini umumnya kurang disarankan dibandingkan interaksi API langsung karena masalah keamanan, portabilitas, dan penanganan kesalahan.

## 8. Troubleshooting Umum

*   **Gagal Otentikasi API**: Pastikan `IP`, `Port`, `Username`, `Password`, dan `Realm` di konfigurasi ekstensi sudah benar dan pengguna memiliki izin yang memadai di Proxmox.
*   **Template Tidak Ditemukan**: Pastikan template yang Anda pilih tersedia di penyimpanan Proxmox yang dikonfigurasi dan nama template di Paymenter sesuai dengan nama di Proxmox.
*   **Tidak Ada IP yang Tersedia**: Periksa `Public IP Range` atau `Private IP Range` di konfigurasi ekstensi. Pastikan rentang IP valid dan ada alamat IP yang belum digunakan.
*   **Tugas Proxmox Gagal**: Periksa log tugas di Proxmox (melalui antarmuka web Proxmox) untuk mendapatkan detail kesalahan yang lebih spesifik. Pesan kesalahan dari API Proxmox akan ditampilkan di Paymenter.

## 9. Kesimpulan

Ekstensi Paymenter Proxmox menyediakan solusi yang kuat untuk mengelola infrastruktur virtual Anda. Dengan konfigurasi yang tepat dan pemahaman tentang fungsionalitasnya, Anda dapat mengotomatisasi banyak aspek penyediaan dan manajemen server untuk klien Anda.