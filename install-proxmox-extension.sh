#!/bin/bash

# ===============================================
# Paymenter Proxmox Extension Installer
# ===============================================

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
PAYMENTER_PATH="/var/www/paymenter"
WEB_USER="www-data"

# Functions
print_header() {
    echo -e "${BLUE}"
    echo "=================================================="
    echo "  Paymenter Proxmox Extension Installer"
    echo "=================================================="
    echo -e "${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

check_requirements() {
    print_info "Checking requirements..."
    
    # Check if running as root
    if [[ $EUID -ne 0 ]]; then
        print_error "This script must be run as root (use sudo)"
        exit 1
    fi
    
    # Check if Paymenter directory exists
    if [[ ! -d "$PAYMENTER_PATH" ]]; then
        print_error "Paymenter directory not found at $PAYMENTER_PATH"
        echo "Please specify the correct path using: $0 /path/to/paymenter"
        exit 1
    fi
    
    # Check if Extensions directory exists
    if [[ ! -d "$PAYMENTER_PATH/app/Extensions/Servers" ]]; then
        print_error "Paymenter Extensions directory not found"
        print_info "Expected: $PAYMENTER_PATH/app/Extensions/Servers"
        exit 1
    fi
    
    print_success "Requirements check passed"
}

install_extension() {
    print_info "Installing Proxmox extension..."
    
    # Create Proxmox extension directory
    EXTENSION_DIR="$PAYMENTER_PATH/app/Extensions/Servers/Proxmox"
    mkdir -p "$EXTENSION_DIR"
    
    # Copy extension files
    if [[ -f "extensions/Servers/Proxmox/Proxmox.php" ]]; then
        cp "extensions/Servers/Proxmox/Proxmox.php" "$EXTENSION_DIR/"
        print_success "Copied Proxmox.php"
    else
        print_error "Proxmox.php not found in extensions/Servers/Proxmox/"
        exit 1
    fi
    
    # Copy README if exists
    if [[ -f "extensions/Servers/Proxmox/README.md" ]]; then
        cp "extensions/Servers/Proxmox/README.md" "$EXTENSION_DIR/"
        print_success "Copied README.md"
    fi
    
    print_success "Extension files installed"
}

set_permissions() {
    print_info "Setting permissions..."
    
    EXTENSION_DIR="$PAYMENTER_PATH/app/Extensions/Servers/Proxmox"
    
    # Set ownership
    chown -R "$WEB_USER:$WEB_USER" "$EXTENSION_DIR"
    print_success "Set ownership to $WEB_USER:$WEB_USER"
    
    # Set permissions
    chmod -R 755 "$EXTENSION_DIR"
    print_success "Set permissions to 755"
}

clear_cache() {
    print_info "Clearing Paymenter cache..."
    
    cd "$PAYMENTER_PATH"
    
    # Clear various caches
    if command -v php >/dev/null 2>&1; then
        php artisan cache:clear >/dev/null 2>&1 || true
        php artisan config:clear >/dev/null 2>&1 || true
        php artisan route:clear >/dev/null 2>&1 || true
        php artisan view:clear >/dev/null 2>&1 || true
        print_success "Cache cleared"
    else
        print_warning "PHP not found in PATH, please clear cache manually"
    fi
}

show_next_steps() {
    echo -e "${GREEN}"
    echo "=================================================="
    echo "  Installation Complete!"
    echo "=================================================="
    echo -e "${NC}"
    
    echo "Next steps:"
    echo "1. Login to Paymenter Admin Panel"
    echo "2. Go to Extensions → Servers"
    echo "3. Configure Proxmox extension with your server details"
    echo "4. Test the connection"
    echo "5. Create products using Proxmox extension"
    echo ""
    echo "Documentation:"
    echo "- Extension README: $PAYMENTER_PATH/app/Extensions/Servers/Proxmox/README.md"
    echo "- Full documentation: PROXMOX_EXTENSION.md"
    echo ""
    print_success "Proxmox extension is ready to use!"
}

# Main execution
main() {
    print_header
    
    # Parse command line arguments
    if [[ $# -gt 0 ]]; then
        PAYMENTER_PATH="$1"
    fi
    
    print_info "Paymenter path: $PAYMENTER_PATH"
    print_info "Web user: $WEB_USER"
    echo ""
    
    # Run installation steps
    check_requirements
    install_extension
    set_permissions
    clear_cache
    show_next_steps
}

# Handle script arguments
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
