<?php

namespace Paymenter\Extensions\Servers\Proxmox;

use App\Classes\Extension\Server;
use App\Models\Product;
use App\Models\Service;
use App\Models\User;
use Illuminate\Http\Client\RequestException;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;

class Proxmox extends Server
{
    private function request($endpoint, $method = 'GET', $data = []): array
    {
        $url = 'https://' . $this->config('ip') . ':' . $this->config('port') . '/api2/json' . $endpoint;
        
        // Get authentication ticket
        $ticket = $this->getAuthTicket();
        
        $headers = [
            'Cookie' => 'PVEAuthCookie=' . $ticket['ticket'],
            'CSRFPreventionToken' => $ticket['CSRFPreventionToken'],
        ];

        if ($method === 'GET') {
            $response = Http::withoutVerifying()
                ->withHeaders($headers)
                ->get($url, $data)
                ->throw();
        } else {
            $response = Http::withoutVerifying()
                ->withHeaders($headers)
                ->asForm()
                ->$method($url, $data)
                ->throw();
        }

        if (!$response->successful()) {
            throw new \Exception('Failed to connect to Proxmox API: ' . $response->body());
        }

        return $response->json();
    }

    private function getAuthTicket(): array
    {
        $authUrl = 'https://' . $this->config('ip') . ':' . $this->config('port') . '/api2/json/access/ticket';
        
        $response = Http::withoutVerifying()
            ->asForm()
            ->post($authUrl, [
                'username' => $this->config('username'),
                'password' => $this->config('password'),
                'realm' => $this->config('realm', 'pve'),
            ])
            ->throw();

        if (!$response->successful()) {
            throw new \Exception('Failed to authenticate with Proxmox');
        }

        $data = $response->json();
        return $data['data'];
    }

    private function getNextVmid(): int
    {
        $response = $this->request('/cluster/nextid');
        return (int) $response['data'];
    }

    private function waitForTask($upid): bool
    {
        $maxAttempts = 60; // 5 minutes timeout
        $attempts = 0;
        
        while ($attempts < $maxAttempts) {
            $response = $this->request('/nodes/' . $this->config('node') . '/tasks/' . $upid . '/status');
            $status = $response['data']['status'];
            
            if ($status === 'stopped') {
                $exitStatus = $response['data']['exitstatus'] ?? null;
                return $exitStatus === 'OK' || $exitStatus === null;
            }
            
            sleep(5);
            $attempts++;
        }
        
        return false;
    }

    private function parseIpRange($range): array
    {
        if (!$range) {
            return [];
        }
        
        $parts = explode(' - ', trim($range));
        if (count($parts) !== 2) {
            throw new \Exception('Invalid IP range format. Use: ************* - *************');
        }
        
        return [
            'start' => ip2long(trim($parts[0])),
            'end' => ip2long(trim($parts[1]))
        ];
    }

    private function getUsedIps(): array
    {
        $services = Service::whereHas('properties', function($query) {
            $query->where('key', 'allocated_ip');
        })->with('properties')->get();
        
        $usedIps = [];
        foreach ($services as $service) {
            $ip = $service->properties()->where('key', 'allocated_ip')->first();
            if ($ip) {
                $usedIps[] = $ip->value;
            }
        }
        
        return $usedIps;
    }

    private function findAvailableIp($networkType = 'public'): ?string
    {
        $rangeKey = $networkType === 'public' ? 'public_ip_range' : 'private_ip_range';
        $range = $this->parseIpRange($this->config($rangeKey));
        
        if (empty($range)) {
            return null;
        }
        
        $usedIps = $this->getUsedIps();
        
        for ($ip = $range['start']; $ip <= $range['end']; $ip++) {
            $ipString = long2ip($ip);
            if (!in_array($ipString, $usedIps)) {
                return $ipString;
            }
        }
        
        return null;
    }

    /**
     * Get all the configuration for the extension
     */
    public function getConfig($values = []): array
    {
        return [
            [
                'name' => 'ip',
                'type' => 'text',
                'label' => 'Proxmox Server IP',
                'required' => true,
            ],
            [
                'name' => 'port',
                'type' => 'text',
                'label' => 'Port',
                'required' => true,
                'default' => '8006',
            ],
            [
                'name' => 'username',
                'type' => 'text',
                'label' => 'Username',
                'required' => true,
                'default' => 'root@pam',
            ],
            [
                'name' => 'password',
                'type' => 'password',
                'label' => 'Password',
                'required' => true,
            ],
            [
                'name' => 'realm',
                'type' => 'text',
                'label' => 'Realm',
                'default' => 'pve',
                'required' => true,
            ],
            [
                'name' => 'node',
                'type' => 'text',
                'label' => 'Default Node',
                'required' => true,
            ],
            [
                'name' => 'storage',
                'type' => 'text',
                'label' => 'Storage',
                'required' => true,
                'default' => 'local-lvm',
            ],
            [
                'name' => 'lxc_storage',
                'type' => 'text',
                'label' => 'LXC Template Storage',
                'required' => false,
                'description' => 'Storage for LXC templates (optional, defaults to main storage)',
            ],
            [
                'name' => 'public_bridge',
                'type' => 'text',
                'label' => 'Public Network Bridge',
                'required' => true,
                'default' => 'vmbr0',
            ],
            [
                'name' => 'public_ip_range',
                'type' => 'text',
                'label' => 'Public IP Range',
                'required' => true,
                'placeholder' => '************* - *************',
                'description' => 'Format: start_ip - end_ip',
            ],
            [
                'name' => 'public_subnet',
                'type' => 'text',
                'label' => 'Public Subnet (CIDR)',
                'required' => true,
                'default' => '24',
            ],
            [
                'name' => 'public_gateway',
                'type' => 'text',
                'label' => 'Public Gateway',
                'required' => true,
            ],
            [
                'name' => 'private_bridge',
                'type' => 'text',
                'label' => 'Private Network Bridge',
                'required' => false,
            ],
            [
                'name' => 'private_ip_range',
                'type' => 'text',
                'label' => 'Private IP Range',
                'required' => false,
                'placeholder' => '********** - **********',
            ],
            [
                'name' => 'private_subnet',
                'type' => 'text',
                'label' => 'Private Subnet (CIDR)',
                'required' => false,
                'default' => '24',
            ],
            [
                'name' => 'private_gateway',
                'type' => 'text',
                'label' => 'Private Gateway',
                'required' => false,
            ],
            [
                'name' => 'dns_servers',
                'type' => 'text',
                'label' => 'DNS Servers',
                'required' => false,
                'placeholder' => '******* *******',
                'description' => 'Space-separated DNS servers',
            ],
        ];
    }

    /**
     * Get product config
     */
    public function getProductConfig($values = []): array
    {
        return [
            [
                'name' => 'vm_type',
                'label' => 'VM Type',
                'type' => 'select',
                'required' => true,
                'options' => [
                    'qemu' => 'KVM/QEMU',
                    'lxc' => 'LXC Container',
                ],
            ],
            [
                'name' => 'cores',
                'label' => 'CPU Cores',
                'type' => 'number',
                'required' => true,
                'default' => 1,
            ],
            [
                'name' => 'memory',
                'label' => 'Memory (MB)',
                'type' => 'number',
                'required' => true,
                'default' => 512,
            ],
            [
                'name' => 'disk_size',
                'label' => 'Disk Size (GB)',
                'type' => 'number',
                'required' => true,
                'default' => 10,
            ],
            [
                'name' => 'bandwidth',
                'label' => 'Bandwidth Limit (MB/s)',
                'type' => 'number',
                'required' => false,
                'default' => 0,
                'description' => 'Set to 0 for unlimited',
            ],
            [
                'name' => 'network_type',
                'label' => 'Network Type',
                'type' => 'select',
                'required' => true,
                'options' => [
                    'public' => 'Public Network',
                    'private' => 'Private Network',
                ],
                'default' => 'public',
            ],
        ];
    }

    public function getCheckoutConfig(Product $product)
    {
        $vmType = $product->settings()->where('key', 'vm_type')->first()->value;
        $templates = [];
        
        try {
            if ($vmType === 'qemu') {
                // Get QEMU templates
                $response = $this->request('/nodes/' . $this->config('node') . '/qemu');
                foreach ($response['data'] as $vm) {
                    if (isset($vm['template']) && $vm['template'] == 1) {
                        $templates[$vm['vmid']] = $vm['name'];
                    }
                }
            } else {
                // Get LXC templates
                $storage = $this->config('lxc_storage') ?: $this->config('storage');
                $response = $this->request('/nodes/' . $this->config('node') . '/storage/' . $storage . '/content');
                foreach ($response['data'] as $template) {
                    if ($template['content'] === 'vztmpl') {
                        $templates[$template['volid']] = basename($template['volid'], '.tar.gz');
                    }
                }
            }
        } catch (\Exception $e) {
            // If we can't get templates, provide empty array
            $templates = [];
        }

        return [
            [
                'name' => 'hostname',
                'type' => 'text',
                'validation' => 'regex:/^(?!:\/\/)(?=.{1,255}$)((.{1,63}\.){1,127}(?![0-9]*$)[a-z0-9-]+\.?)$/i',
                'label' => 'Hostname',
                'placeholder' => 'example.com',
                'required' => true,
            ],
            [
                'name' => 'template',
                'type' => 'select',
                'friendlyName' => 'Template',
                'required' => true,
                'options' => $templates,
            ],
            [
                'name' => 'password',
                'type' => 'password',
                'label' => 'Root Password',
                'required' => false,
                'description' => 'Leave empty for auto-generated password',
            ],
        ];
    }

    /**
     * Check if current configuration is valid
     */
    public function testConfig(): bool|string
    {
        try {
            $this->request('/version');
        } catch (\Exception $e) {
            return $e->getMessage();
        }

        return true;
    }

    /**
     * Create a server
     */
    public function createServer(Service $service, $settings, $properties)
    {
        $settings = array_merge($settings, $properties);
        
        // Get next available VMID
        $vmid = $this->getNextVmid();
        
        // Generate password if not provided
        $password = $settings['password'] ?: Str::random(12);
        
        // Allocate IP address
        $allocatedIp = $this->findAvailableIp($settings['network_type']);
        if (!$allocatedIp) {
            throw new \Exception('No available IP addresses in the configured range');
        }
        
        // Get network configuration
        $networkType = $settings['network_type'];
        $bridge = $networkType === 'public' ? $this->config('public_bridge') : $this->config('private_bridge');
        $subnet = $networkType === 'public' ? $this->config('public_subnet') : $this->config('private_subnet');
        $gateway = $networkType === 'public' ? $this->config('public_gateway') : $this->config('private_gateway');
        
        if ($settings['vm_type'] === 'qemu') {
            // Create QEMU VM by cloning template
            $cloneData = [
                'newid' => $vmid,
                'name' => $settings['hostname'],
                'full' => 1,
                'target' => $this->config('node'),
            ];
            
            $response = $this->request('/nodes/' . $this->config('node') . '/qemu/' . $settings['template'] . '/clone', 'POST', $cloneData);
            
            if (!$this->waitForTask($response['data'])) {
                throw new \Exception('Failed to clone VM template');
            }
            
            // Configure the VM
            $configData = [
                'cores' => $settings['cores'],
                'memory' => $settings['memory'],
                'ciuser' => 'root',
                'ipconfig0' => "ip={$allocatedIp}/{$subnet},gw={$gateway}",
                'net0' => "virtio,bridge={$bridge}",
            ];
            
            if ($password) {
                $configData['cipassword'] = $password;
            }
            
            if ($settings['bandwidth'] > 0) {
                $configData['net0'] .= ",rate={$settings['bandwidth']}";
            }
            
            $response = $this->request('/nodes/' . $this->config('node') . '/qemu/' . $vmid . '/config', 'PUT', $configData);
            
            // Start the VM
            $this->request('/nodes/' . $this->config('node') . '/qemu/' . $vmid . '/status/start', 'POST');
            
        } else {
            // Create LXC Container
            $storage = $this->config('storage');
            $rootfs = $storage . ':' . $settings['disk_size'];
            
            $createData = [
                'vmid' => $vmid,
                'hostname' => $settings['hostname'],
                'cores' => $settings['cores'],
                'memory' => $settings['memory'],
                'rootfs' => $rootfs,
                'net0' => "name=eth0,bridge={$bridge},ip={$allocatedIp}/{$subnet},gw={$gateway}",
                'ostemplate' => $settings['template'],
                'unprivileged' => 1,
            ];
            
            if ($password) {
                $createData['password'] = $password;
            }
            
            if ($settings['bandwidth'] > 0) {
                $createData['net0'] .= ",rate={$settings['bandwidth']}";
            }
            
            if ($this->config('dns_servers')) {
                $createData['nameserver'] = str_replace(' ', ',', $this->config('dns_servers'));
            }
            
            $response = $this->request('/nodes/' . $this->config('node') . '/lxc', 'POST', $createData);
            
            if (!$this->waitForTask($response['data'])) {
                throw new \Exception('Failed to create LXC container');
            }
            
            // Start the container
            $this->request('/nodes/' . $this->config('node') . '/lxc/' . $vmid . '/status/start', 'POST');
        }
        
        // Store service properties
        $service->properties()->updateOrCreate([
            'key' => 'vmid',
        ], [
            'name' => 'VM ID',
            'value' => $vmid,
        ]);
        
        $service->properties()->updateOrCreate([
            'key' => 'vm_type',
        ], [
            'name' => 'VM Type',
            'value' => $settings['vm_type'],
        ]);
        
        $service->properties()->updateOrCreate([
            'key' => 'allocated_ip',
        ], [
            'name' => 'Allocated IP',
            'value' => $allocatedIp,
        ]);
        
        return [
            'vmid' => $vmid,
            'ip' => $allocatedIp,
            'password' => $password,
        ];
    }

    /**
     * Suspend a server
     */
    public function suspendServer(Service $service, $settings, $properties)
    {
        if (!isset($properties['vmid'])) {
            throw new \Exception('VM does not exist');
        }
        
        $vmType = $properties['vm_type'] ?? 'qemu';
        $endpoint = $vmType === 'qemu' ? 'qemu' : 'lxc';
        
        $this->request('/nodes/' . $this->config('node') . '/' . $endpoint . '/' . $properties['vmid'] . '/status/suspend', 'POST');
        
        return true;
    }

    /**
     * Unsuspend a server
     */
    public function unsuspendServer(Service $service, $settings, $properties)
    {
        if (!isset($properties['vmid'])) {
            throw new \Exception('VM does not exist');
        }
        
        $vmType = $properties['vm_type'] ?? 'qemu';
        $endpoint = $vmType === 'qemu' ? 'qemu' : 'lxc';
        
        $this->request('/nodes/' . $this->config('node') . '/' . $endpoint . '/' . $properties['vmid'] . '/status/resume', 'POST');
        
        return true;
    }

    /**
     * Terminate a server
     */
    public function terminateServer(Service $service, $settings, $properties)
    {
        if (!isset($properties['vmid'])) {
            throw new \Exception('VM does not exist');
        }
        
        $vmType = $properties['vm_type'] ?? 'qemu';
        $endpoint = $vmType === 'qemu' ? 'qemu' : 'lxc';
        
        // Stop the VM/Container first
        try {
            $this->request('/nodes/' . $this->config('node') . '/' . $endpoint . '/' . $properties['vmid'] . '/status/stop', 'POST');
            sleep(5); // Wait for stop to complete
        } catch (\Exception $e) {
            // Continue with deletion even if stop fails
        }
        
        // Delete the VM/Container
        $this->request('/nodes/' . $this->config('node') . '/' . $endpoint . '/' . $properties['vmid'], 'DELETE');
        
        // Remove all properties
        $service->properties()->whereIn('key', ['vmid', 'vm_type', 'allocated_ip'])->delete();
        
        return true;
    }

    public function getActions(Service $service, $settings, $properties): array
    {
        if (!isset($properties['vmid'])) {
            return [];
        }

        $actions = [
            [
                'type' => 'button',
                'label' => 'Start VM',
                'function' => 'startVm',
            ],
            [
                'type' => 'button',
                'label' => 'Stop VM',
                'function' => 'stopVm',
            ],
            [
                'type' => 'button',
                'label' => 'Restart VM',
                'function' => 'restartVm',
            ],
        ];
        
        // Add VNC console for QEMU VMs only
        if (($properties['vm_type'] ?? 'qemu') === 'qemu') {
            $actions[] = [
                'type' => 'link',
                'label' => 'VNC Console',
                'function' => 'getVncUrl',
                'target' => '_blank',
            ];
        }
        
        return $actions;
    }

    public function startVm(Service $service, $settings, $properties): void
    {
        if (!isset($properties['vmid'])) {
            throw new \Exception('VM does not exist');
        }
        
        $vmType = $properties['vm_type'] ?? 'qemu';
        $endpoint = $vmType === 'qemu' ? 'qemu' : 'lxc';
        
        try {
            $this->request('/nodes/' . $this->config('node') . '/' . $endpoint . '/' . $properties['vmid'] . '/status/start', 'POST');
        } catch (RequestException $e) {
            // Proxmox may return a 500 error if the VM is already running.
            // We can safely ignore this specific error.
            if ($e->response->status() !== 500) {
                throw $e;
            }
        }
    }

    public function stopVm(Service $service, $settings, $properties): void
    {
        if (!isset($properties['vmid'])) {
            throw new \Exception('VM does not exist');
        }
        
        $vmType = $properties['vm_type'] ?? 'qemu';
        $endpoint = $vmType === 'qemu' ? 'qemu' : 'lxc';
        
        try {
            $this->request('/nodes/' . $this->config('node') . '/' . $endpoint . '/' . $properties['vmid'] . '/status/stop', 'POST');
        } catch (RequestException $e) {
            // Proxmox may return a 500 error if the VM is already stopped.
            // We can safely ignore this specific error.
            if ($e->response->status() !== 500) {
                throw $e;
            }
        }
    }

    public function restartVm(Service $service, $settings, $properties): void
    {
        if (!isset($properties['vmid'])) {
            throw new \Exception('VM does not exist');
        }
        
        $vmType = $properties['vm_type'] ?? 'qemu';
        $endpoint = $vmType === 'qemu' ? 'qemu' : 'lxc';
        
        try {
            $this->request('/nodes/' . $this->config('node') . '/' . $endpoint . '/' . $properties['vmid'] . '/status/reboot', 'POST');
        } catch (RequestException $e) {
            // Reboot can also sometimes throw an error, we can handle it similarly.
            if ($e->response->status() !== 500) {
                throw $e;
            }
        }
    }

    public function getVncUrl(Service $service, $settings, $properties): string
    {
        if (!isset($properties['vmid'])) {
            throw new \Exception('VM does not exist');
        }
        
        if (($properties['vm_type'] ?? 'qemu') !== 'qemu') {
            throw new \Exception('VNC console is only available for QEMU VMs');
        }
        
        $response = $this->request('/nodes/' . $this->config('node') . '/qemu/' . $properties['vmid'] . '/vncproxy', 'POST', [
            'websocket' => 1
        ]);
        
        $vncData = $response['data'];
        
        // Construct the correct noVNC URL with the ticket from the API response.
        $ticket = urlencode($vncData['ticket']);
        $port = $this->config('port');
        $ip = $this->config('ip');
        $node = $this->config('node');
        $vmid = $properties['vmid'];

        return "https://{$ip}:{$port}/?console=kvm&vmid={$vmid}&node={$node}&vncticket={$ticket}";
    }

    public function upgradeServer(Service $service, $settings, $properties)
    {
        if (!isset($properties['vmid'])) {
            throw new \Exception('VM does not exist');
        }
        
        $vmType = $properties['vm_type'] ?? 'qemu';
        $endpoint = $vmType === 'qemu' ? 'qemu' : 'lxc';
        
        $configData = [
            'cores' => $settings['cores'],
            'memory' => $settings['memory'],
        ];
        
        $this->request('/nodes/' . $this->config('node') . '/' . $endpoint . '/' . $properties['vmid'] . '/config', 'PUT', $configData);
        
        return true;
    }
}
