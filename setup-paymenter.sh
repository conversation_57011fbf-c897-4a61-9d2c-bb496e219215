#!/bin/bash

# ------------------- KONFIGURASI DASAR -------------------
APP_DOMAIN="paymenter.local"      # Ganti dengan domain valid (misal: panel.example.com)
APP_NAME="Paymenter"
DB_NAME="paymenter"
DB_USER="paymenter"
DB_PASS="securepassword"
APP_DIR="/var/www/paymenter"
PHP_VERSION="8.2"
ssl_enabled=true                  # Ubah jadi true untuk aktifkan HTTPS otomatis

# ------------------- FUNGSI: SETUP APP -------------------
setup_app() {
    apt update && apt upgrade -y
    apt install -y nginx mariadb-server redis-server certbot python3-certbot-nginx \
    php${PHP_VERSION} php${PHP_VERSION}-cli php${PHP_VERSION}-mbstring php${PHP_VERSION}-xml \
    php${PHP_VERSION}-bcmath php${PHP_VERSION}-curl php${PHP_VERSION}-mysql php${PHP_VERSION}-zip \
    php${PHP_VERSION}-redis unzip git curl nodejs npm

    systemctl enable --now redis

    # Clone Paymenter
    mkdir -p $APP_DIR
    cd /var/www
    if [[ ! -d "$APP_DIR/.git" ]]; then
        git clone https://github.com/Paymenter/Paymenter.git paymenter
    fi
    cd $APP_DIR

    # Composer
    curl -sS https://getcomposer.org/installer | php
    mv composer.phar /usr/local/bin/composer
    composer install --no-dev --optimize-autoloader

    # Build assets
    npm install
    npm run build

    # Laravel config
    cp -n .env.example .env
    sed -i "s|APP_NAME=.*|APP_NAME=\"$APP_NAME\"|" .env
    sed -i "s|APP_URL=.*|APP_URL=http${ssl_enabled:+s}://$APP_DOMAIN|" .env
    sed -i "s|DB_DATABASE=.*|DB_DATABASE=$DB_NAME|" .env
    sed -i "s|DB_USERNAME=.*|DB_USERNAME=$DB_USER|" .env
    sed -i "s|DB_PASSWORD=.*|DB_PASSWORD=$DB_PASS|" .env
    sed -i "s|CACHE_DRIVER=.*|CACHE_DRIVER=redis|" .env
    sed -i "s|QUEUE_CONNECTION=.*|QUEUE_CONNECTION=redis|" .env

    php artisan key:generate
    php artisan storage:link

    chown -R www-data:www-data $APP_DIR
    chmod -R 755 $APP_DIR

    # Hapus config lama
    rm -f /etc/nginx/sites-enabled/paymenter
    rm -f /etc/nginx/sites-available/paymenter

    # Buat config NGINX
    if [ "$ssl_enabled" = true ]; then
        cat >/etc/nginx/sites-available/paymenter <<EOF
server {
    listen 80;
    server_name $APP_DOMAIN;
    root $APP_DIR/public;
    index index.php index.html;

    location / {
        try_files \$uri \$uri/ /index.php?\$query_string;
    }

    location ~ \.php\$ {
        include snippets/fastcgi-php.conf;
        fastcgi_pass unix:/run/php/php${PHP_VERSION}-fpm.sock;
        fastcgi_param SCRIPT_FILENAME \$document_root\$fastcgi_script_name;
        include fastcgi_params;
    }

    location ~ /\. {
        deny all;
    }
}
EOF
    else
        cat >/etc/nginx/sites-available/paymenter <<EOF
server {
    listen 80;
    server_name $APP_DOMAIN;
    root $APP_DIR/public;

    index index.php index.html;

    access_log /var/log/nginx/paymenter.access.log;
    error_log  /var/log/nginx/paymenter.error.log;

    location / {
        try_files \$uri \$uri/ /index.php?\$query_string;
    }

    location = /favicon.ico { access_log off; log_not_found off; }
    location = /robots.txt  { access_log off; log_not_found off; }

    location ~ \.php\$ {
        include snippets/fastcgi-php.conf;
        fastcgi_pass unix:/run/php/php${PHP_VERSION}-fpm.sock;
        fastcgi_param SCRIPT_FILENAME \$document_root\$fastcgi_script_name;
        include fastcgi_params;
    }

    location ~* \.(jpg|jpeg|png|gif|ico|css|js|woff|woff2|ttf|svg|eot)\$ {
        expires max;
        log_not_found off;
    }

    location ~ /\. {
        deny all;
    }

    location ~ ^/(storage|vendor)/ {
        deny all;
    }

    location ~ ^/\.env {
        deny all;
    }
}
EOF
    fi

    ln -s /etc/nginx/sites-available/paymenter /etc/nginx/sites-enabled/
    nginx -t && systemctl reload nginx

    # Jalankan certbot jika ssl_enabled true
    if [ "$ssl_enabled" = true ]; then
        certbot --nginx -d $APP_DOMAIN --non-interactive --agree-tos -m admin@$APP_DOMAIN
    fi

    echo "✅ Setup aplikasi selesai. Akses di: http${ssl_enabled:+s}://$APP_DOMAIN"
}

# ------------------- FUNGSI: SETUP DB -------------------
setup_db() {
    systemctl start mariadb

    mysql -e "CREATE DATABASE IF NOT EXISTS ${DB_NAME};"
    mysql -e "CREATE USER IF NOT EXISTS '${DB_USER}'@'localhost' IDENTIFIED BY '${DB_PASS}';"
    mysql -e "GRANT ALL PRIVILEGES ON ${DB_NAME}.* TO '${DB_USER}'@'localhost';"
    mysql -e "FLUSH PRIVILEGES;"

    cd $APP_DIR
    php artisan migrate --force

    echo "✅ Database berhasil dibuat dan dimigrasi."
}

# ------------------- FUNGSI: SETUP WORKER -------------------
setup_worker() {
    CRON_FILE="/etc/cron.d/paymenter"
    cat > $CRON_FILE <<EOF
* * * * * www-data cd $APP_DIR && php artisan schedule:run >> /dev/null 2>&1
EOF
    chmod 644 $CRON_FILE

    cat >/etc/systemd/system/paymenter-worker.service <<EOF
[Unit]
Description=Laravel Paymenter Queue Worker
After=network.target

[Service]
User=www-data
Group=www-data
Restart=always
ExecStart=/usr/bin/php $APP_DIR/artisan queue:work --sleep=3 --tries=3

[Install]
WantedBy=multi-user.target
EOF

    systemctl daemon-reexec
    systemctl daemon-reload
    systemctl enable --now paymenter-worker

    echo "✅ Cronjob dan worker service berhasil diaktifkan."
}

# ------------------- SETUP REMOTE WORKER (server terpisah) -------------------
setup_remote_worker() {
    apt update && apt upgrade -y
    apt install -y php${PHP_VERSION} php${PHP_VERSION}-cli \
    php${PHP_VERSION}-mbstring php${PHP_VERSION}-xml php${PHP_VERSION}-bcmath \
    php${PHP_VERSION}-curl php${PHP_VERSION}-redis unzip git curl 

    curl -sS https://getcomposer.org/installer | php
    mv composer.phar /usr/local/bin/composer

    if [[ ! -d "$APP_DIR" ]]; then
        git clone https://github.com/Paymenter/Paymenter.git $APP_DIR
    fi

    cd $APP_DIR
    composer install --no-dev --optimize-autoloader

    if [[ ! -f ".env" ]]; then
        cp .env.example .env
        sed -i "s|QUEUE_CONNECTION=.*|QUEUE_CONNECTION=redis|" .env
        sed -i "s|REDIS_HOST=.*|REDIS_HOST=${REMOTE_REDIS_HOST}|" .env
        sed -i "s|REDIS_PORT=.*|REDIS_PORT=${REMOTE_REDIS_PORT}|" .env
        echo "REDIS_PASSWORD=$REMOTE_REDIS_PASSWORD" >> .env
        php artisan key:generate
    fi

    cat >/etc/systemd/system/paymenter-worker.service <<EOF
[Unit]
Description=Laravel Paymenter Remote Queue Worker
After=network.target

[Service]
User=www-data
Group=www-data
Restart=always
ExecStart=/usr/bin/php $APP_DIR/artisan queue:work --sleep=3 --tries=3 --timeout=90

[Install]
WantedBy=multi-user.target
EOF

    systemctl daemon-reload
    systemctl enable --now paymenter-worker

    echo "✅ Worker aktif di server worker terpisah (Redis host: $REMOTE_REDIS_HOST)"
}

# ------------------- MAIN -------------------
case "$1" in
    app)
        setup_app
        ;;
    db)
        setup_db
        ;;
    worker)
        setup_worker
        ;;
    remote-worker)
        setup_remote_worker
        ;;
    *)
        echo "Usage: $0 {app|db|worker|remote-worker}"
        exit 1
        ;;
esac