# 📦 Panduan Instalasi Extension Proxmox untuk Paymenter

Panduan lengkap untuk menginstal dan mengkonfigurasi extension Proxmox di Paymenter.

## 📋 Persyaratan Sistem

### Paymenter
- Paymenter v2.0 atau lebih baru
- PHP 8.1 atau lebih baru
- Laravel 9+ (bias<PERSON>a sudah termasuk dalam Paymenter)

### Proxmox VE
- Proxmox VE 7.0 atau lebih baru
- API access enabled (default: port 8006)
- User dengan permission yang memadai

### Server Requirements
- Koneksi network antara Paymenter dan Proxmox
- Firewall yang mengizinkan akses ke port 8006 Proxmox

## 🚀 Metode Instalasi

### Metode 1: Instalasi Otomatis (Recommended)

```bash
# Clone repository atau download files
git clone <repository-url>
cd pve

# Jalankan installer (sebagai root/sudo)
sudo ./install-proxmox-extension.sh

# Atau jika Paymenter di lokasi custom
sudo ./install-proxmox-extension.sh /path/to/paymenter
```

### Metode 2: Instalasi Manual

#### Step 1: Copy Extension Files

```bash
# Buat direktori extension
sudo mkdir -p /var/www/paymenter/app/Extensions/Servers/Proxmox

# Copy file extension
sudo cp extensions/Servers/Proxmox/Proxmox.php /var/www/paymenter/app/Extensions/Servers/Proxmox/

# Copy dokumentasi (opsional)
sudo cp extensions/Servers/Proxmox/README.md /var/www/paymenter/app/Extensions/Servers/Proxmox/
```

#### Step 2: Set Permissions

```bash
# Set ownership
sudo chown -R www-data:www-data /var/www/paymenter/app/Extensions/Servers/Proxmox

# Set permissions
sudo chmod -R 755 /var/www/paymenter/app/Extensions/Servers/Proxmox
```

#### Step 3: Clear Cache

```bash
cd /var/www/paymenter
sudo -u www-data php artisan cache:clear
sudo -u www-data php artisan config:clear
sudo -u www-data php artisan route:clear
```

## ⚙️ Konfigurasi Extension

### 1. Akses Admin Panel

1. Login ke Paymenter Admin Panel
2. Navigate ke **Extensions** → **Servers**
3. Cari **Proxmox** dalam daftar extension
4. Klik **Configure**

### 2. Konfigurasi Server

#### Konfigurasi Wajib

| Field | Deskripsi | Contoh |
|-------|-----------|---------|
| **Proxmox Server IP** | IP atau hostname Proxmox | `*************` |
| **Port** | Port API Proxmox | `8006` |
| **Username** | Username Proxmox | `root@pam` |
| **Password** | Password user | `your_password` |
| **Realm** | Authentication realm | `pve` |
| **Default Node** | Node default untuk VM | `pve` |
| **Storage** | Storage default | `local-lvm` |
| **Public Network Bridge** | Bridge network public | `vmbr0` |
| **Public IP Range** | Range IP public | `************* - *************` |
| **Public Subnet (CIDR)** | Subnet mask | `24` |
| **Public Gateway** | Gateway IP | `***********` |

#### Konfigurasi Opsional

| Field | Deskripsi | Contoh |
|-------|-----------|---------|
| **LXC Template Storage** | Storage khusus LXC | `local` |
| **Private Network Bridge** | Bridge network private | `vmbr1` |
| **Private IP Range** | Range IP private | `********** - **********` |
| **Private Subnet (CIDR)** | Subnet private | `24` |
| **Private Gateway** | Gateway private | `********` |
| **DNS Servers** | DNS servers | `******* *******` |

### 3. Test Koneksi

1. Setelah mengisi konfigurasi, klik **Test Connection**
2. Pastikan muncul pesan sukses
3. Jika gagal, periksa:
   - IP dan port Proxmox
   - Username dan password
   - Firewall settings
   - Network connectivity

## 🛠️ Setup Proxmox

### 1. Persiapan User Proxmox

```bash
# Login ke Proxmox sebagai root
# Buat user khusus untuk Paymenter (opsional)
pveum user add paymenter@pve --password your_password

# Atau gunakan user root@pam yang sudah ada
```

### 2. Set Permissions

```bash
# Berikan permission yang diperlukan
pveum acl modify / --users paymenter@pve --roles Administrator

# Atau untuk permission lebih terbatas
pveum role add PaymenterRole --privs "VM.Allocate,VM.Clone,VM.Config.Disk,VM.Config.CPU,VM.Config.Memory,VM.Config.Network,VM.Monitor,VM.PowerMgmt,Datastore.AllocateSpace"
pveum acl modify / --users paymenter@pve --roles PaymenterRole
```

### 3. Persiapan Templates

#### Untuk KVM/QEMU:
```bash
# Download template (contoh Ubuntu)
wget https://cloud-images.ubuntu.com/focal/current/focal-server-cloudimg-amd64.img

# Import sebagai template
qm create 9000 --name ubuntu-20.04-template --memory 1024 --cores 1 --net0 virtio,bridge=vmbr0
qm importdisk 9000 focal-server-cloudimg-amd64.img local-lvm
qm set 9000 --scsihw virtio-scsi-pci --scsi0 local-lvm:vm-9000-disk-0
qm set 9000 --boot c --bootdisk scsi0
qm set 9000 --ide2 local-lvm:cloudinit
qm set 9000 --serial0 socket --vga serial0
qm template 9000
```

#### Untuk LXC:
```bash
# Download template LXC
pveam update
pveam available
pveam download local ubuntu-20.04-standard_20.04-1_amd64.tar.gz
```

## 🎯 Membuat Produk

### 1. Buat Produk Baru

1. Go to **Products** → **Create Product**
2. Pilih **Extension**: Proxmox
3. Set pricing dan details

### 2. Konfigurasi Produk

| Setting | Deskripsi | Contoh |
|---------|-----------|---------|
| **VM Type** | Tipe virtualisasi | `KVM/QEMU` atau `LXC Container` |
| **CPU Cores** | Jumlah CPU cores | `1` |
| **Memory (MB)** | RAM dalam MB | `1024` |
| **Disk Size (GB)** | Storage dalam GB | `20` |
| **Bandwidth Limit** | Limit bandwidth (0=unlimited) | `0` |
| **Network Type** | Tipe network | `Public Network` |

### 3. Test Order

1. Buat test order dari customer panel
2. Pilih template yang tersedia
3. Set hostname dan password
4. Complete order
5. Verify VM/Container dibuat di Proxmox

## 🔍 Testing

### Manual Testing

```bash
# Test extension secara manual
php test-proxmox-extension.php
```

### Test Checklist

- [ ] Extension terdeteksi di admin panel
- [ ] Konfigurasi tersimpan dengan benar
- [ ] Test connection berhasil
- [ ] Template list muncul di checkout
- [ ] VM/Container berhasil dibuat
- [ ] IP allocation bekerja
- [ ] Start/Stop/Restart berfungsi
- [ ] VNC console accessible (untuk KVM)
- [ ] Terminate menghapus VM dan IP

## 🚨 Troubleshooting

### Extension Tidak Muncul

```bash
# Clear cache
php artisan cache:clear
php artisan config:clear

# Check file permissions
ls -la app/Extensions/Servers/Proxmox/

# Check PHP errors
tail -f storage/logs/laravel.log
```

### Connection Failed

1. **Check network connectivity**:
   ```bash
   curl -k https://proxmox-ip:8006/api2/json/version
   ```

2. **Check credentials**:
   - Login manual ke Proxmox web interface
   - Verify username format (user@realm)

3. **Check firewall**:
   ```bash
   # Di server Proxmox
   iptables -L | grep 8006
   ```

### VM Creation Failed

1. **Check Proxmox logs**:
   - Web interface → Node → System → Syslog
   - `/var/log/pve/tasks/`

2. **Check templates**:
   ```bash
   # List templates
   qm list | grep template
   pct list | grep template
   ```

3. **Check storage**:
   ```bash
   pvesm status
   ```

## 📚 Resources

- [Proxmox API Documentation](https://pve.proxmox.com/pve-docs/api-viewer/)
- [Paymenter Documentation](https://paymenter.org/docs)
- [Extension README](extensions/Servers/Proxmox/README.md)
- [Technical Documentation](PROXMOX_EXTENSION.md)

## 🆘 Support

Jika mengalami masalah:

1. Check logs di `storage/logs/laravel.log`
2. Check Proxmox task logs
3. Verify semua konfigurasi
4. Test API connection manual
5. Check file permissions dan ownership
