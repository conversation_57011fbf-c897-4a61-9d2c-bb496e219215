{"info": {"name": "Proxmox API Collection", "_postman_id": "proxmox-api-collection", "description": "Postman collection generated from scripts that interact with Proxmox API.", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "auth", "item": [{"name": "POST /api2/json/access/ticket", "request": {"method": "POST", "header": [{"key": "<PERSON><PERSON>", "value": "PVEAuthCookie={{pve_ticket}}", "type": "text"}, {"key": "CSRFPreventionToken", "value": "{{pve_csrf}}", "type": "text"}], "url": {"raw": "{{pve_host}}/api2/json/access/ticket", "host": ["{{pve_host}}"], "path": ["api2", "json", "access", "ticket"]}}, "response": []}]}, {"name": "create-kvm", "item": [{"name": "POST /api2/json/nodes/{{node}}/qemu/{{template_id}}/clone", "request": {"method": "POST", "header": [{"key": "<PERSON><PERSON>", "value": "PVEAuthCookie={{pve_ticket}}", "type": "text"}, {"key": "CSRFPreventionToken", "value": "{{pve_csrf}}", "type": "text"}], "url": {"raw": "{{pve_host}}/api2/json/nodes/{{node}}/qemu/{{template_id}}/clone", "host": ["{{pve_host}}"], "path": ["api2", "json", "nodes", "{{node}}", "qemu", "{{template_id}}", "clone"]}}, "response": []}, {"name": "POST /api2/json/nodes/{{node}}/qemu/{{vmid}}/config", "request": {"method": "POST", "header": [{"key": "<PERSON><PERSON>", "value": "PVEAuthCookie={{pve_ticket}}", "type": "text"}, {"key": "CSRFPreventionToken", "value": "{{pve_csrf}}", "type": "text"}], "url": {"raw": "{{pve_host}}/api2/json/nodes/{{node}}/qemu/{{vmid}}/config", "host": ["{{pve_host}}"], "path": ["api2", "json", "nodes", "{{node}}", "qemu", "{{vmid}}", "config"]}}, "response": []}, {"name": "POST /api2/json/nodes/{{node}}/qemu/{{vmid}}/status/start", "request": {"method": "POST", "header": [{"key": "<PERSON><PERSON>", "value": "PVEAuthCookie={{pve_ticket}}", "type": "text"}, {"key": "CSRFPreventionToken", "value": "{{pve_csrf}}", "type": "text"}], "url": {"raw": "{{pve_host}}/api2/json/nodes/{{node}}/qemu/{{vmid}}/status/start", "host": ["{{pve_host}}"], "path": ["api2", "json", "nodes", "{{node}}", "qemu", "{{vmid}}", "status", "start"]}}, "response": []}]}, {"name": "destroy-kvm", "item": [{"name": "DELETE /api2/json/nodes/{{node}}/qemu/{{vmid}}", "request": {"method": "DELETE", "header": [{"key": "<PERSON><PERSON>", "value": "PVEAuthCookie={{pve_ticket}}", "type": "text"}, {"key": "CSRFPreventionToken", "value": "{{pve_csrf}}", "type": "text"}], "url": {"raw": "{{pve_host}}/api2/json/nodes/{{node}}/qemu/{{vmid}}", "host": ["{{pve_host}}"], "path": ["api2", "json", "nodes", "{{node}}", "qemu", "{{vmid}}"]}}, "response": []}]}, {"name": "destroy-lxc", "item": [{"name": "DELETE /api2/json/nodes/{{node}}/lxc/{{vmid}}", "request": {"method": "DELETE", "header": [{"key": "<PERSON><PERSON>", "value": "PVEAuthCookie={{pve_ticket}}", "type": "text"}, {"key": "CSRFPreventionToken", "value": "{{pve_csrf}}", "type": "text"}], "url": {"raw": "{{pve_host}}/api2/json/nodes/{{node}}/lxc/{{vmid}}", "host": ["{{pve_host}}"], "path": ["api2", "json", "nodes", "{{node}}", "lxc", "{{vmid}}"]}}, "response": []}]}, {"name": "get-lxc-template", "item": [{"name": "GET /api2/json/nodes/{{node}}/storage/{{storage}}/content", "request": {"method": "GET", "header": [{"key": "<PERSON><PERSON>", "value": "PVEAuthCookie={{pve_ticket}}", "type": "text"}, {"key": "CSRFPreventionToken", "value": "{{pve_csrf}}", "type": "text"}], "url": {"raw": "{{pve_host}}/api2/json/nodes/{{node}}/storage/{{storage}}/content", "host": ["{{pve_host}}"], "path": ["api2", "json", "nodes", "{{node}}", "storage", "{{storage}}", "content"]}}, "response": []}]}, {"name": "resume-kvm", "item": [{"name": "POST /api2/json/nodes/{{node}}/qemu/{{vmid}}/status/resume", "request": {"method": "POST", "header": [{"key": "<PERSON><PERSON>", "value": "PVEAuthCookie={{pve_ticket}}", "type": "text"}, {"key": "CSRFPreventionToken", "value": "{{pve_csrf}}", "type": "text"}], "url": {"raw": "{{pve_host}}/api2/json/nodes/{{node}}/qemu/{{vmid}}/status/resume", "host": ["{{pve_host}}"], "path": ["api2", "json", "nodes", "{{node}}", "qemu", "{{vmid}}", "status", "resume"]}}, "response": []}]}, {"name": "resume-lxc", "item": [{"name": "POST /api2/json/nodes/{{node}}/lxc/{{vmid}}/status/resume", "request": {"method": "POST", "header": [{"key": "<PERSON><PERSON>", "value": "PVEAuthCookie={{pve_ticket}}", "type": "text"}, {"key": "CSRFPreventionToken", "value": "{{pve_csrf}}", "type": "text"}], "url": {"raw": "{{pve_host}}/api2/json/nodes/{{node}}/lxc/{{vmid}}/status/resume", "host": ["{{pve_host}}"], "path": ["api2", "json", "nodes", "{{node}}", "lxc", "{{vmid}}", "status", "resume"]}}, "response": []}]}, {"name": "start-kvm", "item": [{"name": "POST /api2/json/nodes/{{node}}/qemu/{{vmid}}/status/start", "request": {"method": "POST", "header": [{"key": "<PERSON><PERSON>", "value": "PVEAuthCookie={{pve_ticket}}", "type": "text"}, {"key": "CSRFPreventionToken", "value": "{{pve_csrf}}", "type": "text"}], "url": {"raw": "{{pve_host}}/api2/json/nodes/{{node}}/qemu/{{vmid}}/status/start", "host": ["{{pve_host}}"], "path": ["api2", "json", "nodes", "{{node}}", "qemu", "{{vmid}}", "status", "start"]}}, "response": []}]}, {"name": "start-lxc", "item": [{"name": "POST /api2/json/nodes/{{node}}/lxc/{{vmid}}/status/start", "request": {"method": "POST", "header": [{"key": "<PERSON><PERSON>", "value": "PVEAuthCookie={{pve_ticket}}", "type": "text"}, {"key": "CSRFPreventionToken", "value": "{{pve_csrf}}", "type": "text"}], "url": {"raw": "{{pve_host}}/api2/json/nodes/{{node}}/lxc/{{vmid}}/status/start", "host": ["{{pve_host}}"], "path": ["api2", "json", "nodes", "{{node}}", "lxc", "{{vmid}}", "status", "start"]}}, "response": []}]}, {"name": "stop-kvm", "item": [{"name": "POST /api2/json/nodes/{{node}}/qemu/{{vmid}}/status/stop", "request": {"method": "POST", "header": [{"key": "<PERSON><PERSON>", "value": "PVEAuthCookie={{pve_ticket}}", "type": "text"}, {"key": "CSRFPreventionToken", "value": "{{pve_csrf}}", "type": "text"}], "url": {"raw": "{{pve_host}}/api2/json/nodes/{{node}}/qemu/{{vmid}}/status/stop", "host": ["{{pve_host}}"], "path": ["api2", "json", "nodes", "{{node}}", "qemu", "{{vmid}}", "status", "stop"]}}, "response": []}]}, {"name": "stop-lxc", "item": [{"name": "POST /api2/json/nodes/{{node}}/lxc/{{vmid}}/status/stop", "request": {"method": "POST", "header": [{"key": "<PERSON><PERSON>", "value": "PVEAuthCookie={{pve_ticket}}", "type": "text"}, {"key": "CSRFPreventionToken", "value": "{{pve_csrf}}", "type": "text"}], "url": {"raw": "{{pve_host}}/api2/json/nodes/{{node}}/lxc/{{vmid}}/status/stop", "host": ["{{pve_host}}"], "path": ["api2", "json", "nodes", "{{node}}", "lxc", "{{vmid}}", "status", "stop"]}}, "response": []}]}, {"name": "suspend-kvm", "item": [{"name": "POST /api2/json/nodes/{{node}}/qemu/{{vmid}}/status/suspend", "request": {"method": "POST", "header": [{"key": "<PERSON><PERSON>", "value": "PVEAuthCookie={{pve_ticket}}", "type": "text"}, {"key": "CSRFPreventionToken", "value": "{{pve_csrf}}", "type": "text"}], "url": {"raw": "{{pve_host}}/api2/json/nodes/{{node}}/qemu/{{vmid}}/status/suspend", "host": ["{{pve_host}}"], "path": ["api2", "json", "nodes", "{{node}}", "qemu", "{{vmid}}", "status", "suspend"]}}, "response": []}]}, {"name": "suspend-lxc", "item": [{"name": "POST /api2/json/nodes/{{node}}/lxc/{{vmid}}/status/suspend", "request": {"method": "POST", "header": [{"key": "<PERSON><PERSON>", "value": "PVEAuthCookie={{pve_ticket}}", "type": "text"}, {"key": "CSRFPreventionToken", "value": "{{pve_csrf}}", "type": "text"}], "url": {"raw": "{{pve_host}}/api2/json/nodes/{{node}}/lxc/{{vmid}}/status/suspend", "host": ["{{pve_host}}"], "path": ["api2", "json", "nodes", "{{node}}", "lxc", "{{vmid}}", "status", "suspend"]}}, "response": []}]}]}